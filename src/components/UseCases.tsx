import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Row,
  Col,
  Divider,
  Badge,
  Tooltip,
  Progress,
  Timeline,
  Alert,
  List
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  FolderOutlined,
  AppstoreOutlined,
  SafetyOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { mockUseCases, mockSoftwareApplications, mockApprovalRecords } from '../data/governanceMockData';

// Define types locally to avoid circular dependency
type UseCase = any;
type SoftwareApplication = any;
type ApprovalRecord = any;

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface UseCasesProps {
  useCases?: UseCase[];
  applications?: SoftwareApplication[];
  approvalRecords?: ApprovalRecord[];
  onAddUseCase?: (useCase: UseCase) => void;
  onEditUseCase?: (useCase: UseCase) => void;
}

const UseCases: React.FC<UseCasesProps> = ({
  useCases = mockUseCases,
  applications = mockSoftwareApplications,
  approvalRecords = mockApprovalRecords,
  onAddUseCase,
  onEditUseCase
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [editingUseCase, setEditingUseCase] = useState<UseCase | null>(null);
  const [selectedUseCase, setSelectedUseCase] = useState<UseCase | null>(null);
  const [form] = Form.useForm();

  const handleAdd = () => {
    setEditingUseCase(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (useCase: UseCase) => {
    setEditingUseCase(useCase);
    form.setFieldsValue({
      name: useCase.name,
      description: useCase.description,
      businessJustification: useCase.businessJustification,
      legalBasis: useCase.legalBasis,
      dataSubjects: useCase.dataSubjects,
      owner: useCase.owner,
      stakeholders: useCase.stakeholders
    });
    setIsModalVisible(true);
  };

  const handleView = (useCase: UseCase) => {
    setSelectedUseCase(useCase);
    setIsDetailModalVisible(true);
  };

  const handleSubmit = async (values: any) => {
    const useCase: UseCase = {
      id: editingUseCase?.id || `uc-${Date.now()}`,
      name: values.name,
      description: values.description,
      businessJustification: values.businessJustification,
      legalBasis: values.legalBasis,
      dataSubjects: values.dataSubjects || [],
      applications: editingUseCase?.applications || [],
      riskAssessment: editingUseCase?.riskAssessment || {
        overallRisk: 'medium',
        riskFactors: [],
        mitigationMeasures: [],
        residualRisk: 'low',
        reviewDate: new Date().toISOString().split('T')[0],
        nextReviewDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      },
      owner: values.owner,
      stakeholders: values.stakeholders || [],
      auditRecords: editingUseCase?.auditRecords || [],
      createdAt: editingUseCase?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    if (editingUseCase) {
      onEditUseCase?.(useCase);
    } else {
      onAddUseCase?.(useCase);
    }

    setIsModalVisible(false);
    form.resetFields();
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'success';
      case 'medium': return 'warning';
      case 'high': return 'error';
      case 'very-high': return 'error';
      default: return 'default';
    }
  };

  const getApprovalStatus = (useCaseId: string) => {
    const approval = approvalRecords.find(ar => ar.useCaseId === useCaseId);
    return approval?.finalApprovalStatus || 'pending';
  };

  const getApprovalStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'success';
      case 'rejected': return 'error';
      case 'conditional': return 'warning';
      case 'pending': return 'default';
      default: return 'default';
    }
  };

  const getApprovalStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircleOutlined />;
      case 'rejected': return <ExclamationCircleOutlined />;
      case 'conditional': return <ClockCircleOutlined />;
      case 'pending': return <ClockCircleOutlined />;
      default: return null;
    }
  };

  const columns = [
    {
      title: 'Use Case',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: UseCase) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
            <FolderOutlined style={{ color: '#007AFF' }} />
            <Text strong style={{ color: '#1d1d1f', fontSize: '16px' }}>{text}</Text>
          </div>
          <Text style={{ color: '#86868b', fontSize: '14px' }}>{record.description}</Text>
        </div>
      ),
    },
    {
      title: 'Applications',
      dataIndex: 'applications',
      key: 'applications',
      render: (applicationIds: string[]) => (
        <div>
          <Badge count={applicationIds.length} style={{ backgroundColor: '#007AFF' }}>
            <AppstoreOutlined style={{ fontSize: '16px', color: '#86868b' }} />
          </Badge>
          <div style={{ marginTop: '4px' }}>
            {applicationIds.slice(0, 2).map(appId => {
              const app = applications.find(a => a.id === appId);
              return app ? (
                <Tag key={appId} style={{ borderRadius: '6px', marginBottom: '2px', fontSize: '11px' }}>
                  {app.name}
                </Tag>
              ) : null;
            })}
            {applicationIds.length > 2 && (
              <Text style={{ color: '#86868b', fontSize: '12px' }}>
                +{applicationIds.length - 2} more
              </Text>
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'Legal Basis',
      dataIndex: 'legalBasis',
      key: 'legalBasis',
      render: (legalBasis: string) => (
        <Tag color="blue" style={{ borderRadius: '6px' }}>
          {legalBasis.length > 20 ? `${legalBasis.substring(0, 20)}...` : legalBasis}
        </Tag>
      ),
    },
    {
      title: 'Risk Level',
      key: 'riskLevel',
      render: (record: UseCase) => (
        <Tag
          color={getRiskColor(record.riskAssessment.overallRisk)}
          style={{ borderRadius: '6px', fontWeight: 500 }}
        >
          {record.riskAssessment.overallRisk.replace('-', ' ').toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Approval Status',
      key: 'approvalStatus',
      render: (record: UseCase) => {
        const status = getApprovalStatus(record.id);
        return (
          <Tag
            color={getApprovalStatusColor(status)}
            icon={getApprovalStatusIcon(status)}
            style={{ borderRadius: '6px', fontWeight: 500 }}
          >
            {status.replace('-', ' ').toUpperCase()}
          </Tag>
        );
      },
    },
    {
      title: 'Owner',
      dataIndex: 'owner',
      key: 'owner',
      render: (owner: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
          <UserOutlined style={{ color: '#86868b' }} />
          <Text style={{ color: '#1d1d1f', fontSize: '14px' }}>{owner}</Text>
        </div>
      ),
    },
    {
      title: 'Data Subjects',
      dataIndex: 'dataSubjects',
      key: 'dataSubjects',
      render: (dataSubjects: string[]) => (
        <div>
          {dataSubjects.slice(0, 2).map(subject => (
            <Tag key={subject} style={{ borderRadius: '6px', marginBottom: '2px', fontSize: '11px' }}>
              {subject}
            </Tag>
          ))}
          {dataSubjects.length > 2 && (
            <Text style={{ color: '#86868b', fontSize: '12px' }}>
              +{dataSubjects.length - 2} more
            </Text>
          )}
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: UseCase) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            style={{ color: '#007AFF' }}
          >
            View
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            style={{ color: '#007AFF' }}
          >
            Edit
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '0 4px' }}>
      <div style={{ marginBottom: '32px' }}>
        <Title level={1} style={{
          color: '#1d1d1f',
          fontSize: '34px',
          fontWeight: 700,
          margin: 0,
          marginBottom: '8px',
          letterSpacing: '-0.5px'
        }}>
          Use Cases
        </Title>
        <Text style={{
          color: '#86868b',
          fontSize: '17px',
          fontWeight: 400,
          lineHeight: '22px'
        }}>
          Define business justifications and group applications for comprehensive data governance
        </Text>
      </div>

      <div style={{ marginBottom: '24px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          size="large"
          style={{
            borderRadius: '12px',
            height: '48px',
            fontSize: '16px',
            fontWeight: 500,
            boxShadow: '0 4px 12px rgba(0, 122, 255, 0.3)'
          }}
        >
          Create New Use Case
        </Button>
      </div>

      <Card
        style={{
          borderRadius: '16px',
          border: '1px solid #e5e5e7',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
        }}
      >
        <Table
          columns={columns}
          dataSource={useCases}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} use cases`,
          }}
          style={{ marginTop: '16px' }}
        />
      </Card>

      {/* Add/Edit Modal */}
      <Modal
        title={editingUseCase ? 'Edit Use Case' : 'Create New Use Case'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ marginTop: '24px' }}
        >
          <Form.Item
            name="name"
            label="Use Case Name"
            rules={[{ required: true, message: 'Please enter use case name' }]}
          >
            <Input placeholder="E-commerce Platform Operations" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea rows={3} placeholder="Describe the overall purpose and scope of this use case" />
          </Form.Item>

          <Form.Item
            name="businessJustification"
            label="Business Justification"
            rules={[{ required: true, message: 'Please enter business justification' }]}
          >
            <TextArea rows={3} placeholder="Explain the business need and value proposition" />
          </Form.Item>

          <Form.Item
            name="legalBasis"
            label="Legal Basis"
            rules={[{ required: true, message: 'Please enter legal basis' }]}
          >
            <Select placeholder="Select legal basis for data processing">
              <Option value="consent">Consent</Option>
              <Option value="contract">Contract Performance</Option>
              <Option value="legal-obligation">Legal Obligation</Option>
              <Option value="vital-interests">Vital Interests</Option>
              <Option value="public-task">Public Task</Option>
              <Option value="legitimate-interests">Legitimate Interests</Option>
              <Option value="contract-and-legitimate-interests">Contract Performance and Legitimate Interests</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="dataSubjects"
            label="Data Subjects"
            rules={[{ required: true, message: 'Please specify data subjects' }]}
          >
            <Select mode="tags" placeholder="Enter data subject categories (e.g., Customers, Employees)">
              <Option value="Customers">Customers</Option>
              <Option value="Employees">Employees</Option>
              <Option value="Website visitors">Website Visitors</Option>
              <Option value="Job applicants">Job Applicants</Option>
              <Option value="Vendors">Vendors</Option>
              <Option value="Partners">Partners</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="owner"
                label="Business Owner"
                rules={[{ required: true, message: 'Please enter business owner' }]}
              >
                <Input placeholder="Sarah Johnson, VP of Digital Commerce" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="stakeholders"
                label="Stakeholders"
              >
                <Select mode="tags" placeholder="Enter stakeholder teams/roles">
                  <Option value="Marketing Team">Marketing Team</Option>
                  <Option value="Customer Service">Customer Service</Option>
                  <Option value="IT Security">IT Security</Option>
                  <Option value="Legal Department">Legal Department</Option>
                  <Option value="HR Team">HR Team</Option>
                  <Option value="Product Team">Product Team</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'right', marginTop: '24px' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {editingUseCase ? 'Update Use Case' : 'Create Use Case'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* Detail Modal */}
      <Modal
        title="Use Case Details"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={null}
        width={900}
        style={{ top: 20 }}
      >
        {selectedUseCase && (
          <div style={{ marginTop: '24px' }}>
            <Row gutter={24}>
              <Col span={16}>
                <Card title="Overview" style={{ marginBottom: '16px' }}>
                  <p><strong>Description:</strong> {selectedUseCase.description}</p>
                  <p><strong>Business Justification:</strong> {selectedUseCase.businessJustification}</p>
                  <p><strong>Legal Basis:</strong> {selectedUseCase.legalBasis}</p>
                </Card>

                <Card title="Applications" style={{ marginBottom: '16px' }}>
                  <List
                    dataSource={selectedUseCase.applications}
                    renderItem={appId => {
                      const app = applications.find(a => a.id === appId);
                      return app ? (
                        <List.Item>
                          <List.Item.Meta
                            avatar={<AppstoreOutlined style={{ color: '#007AFF' }} />}
                            title={app.name}
                            description={app.description}
                          />
                        </List.Item>
                      ) : null;
                    }}
                  />
                </Card>
              </Col>

              <Col span={8}>
                <Card title="Risk Assessment" style={{ marginBottom: '16px' }}>
                  <div style={{ marginBottom: '16px' }}>
                    <Text strong>Overall Risk: </Text>
                    <Tag color={getRiskColor(selectedUseCase.riskAssessment.overallRisk)}>
                      {selectedUseCase.riskAssessment.overallRisk.toUpperCase()}
                    </Tag>
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <Text strong>Residual Risk: </Text>
                    <Tag color={getRiskColor(selectedUseCase.riskAssessment.residualRisk)}>
                      {selectedUseCase.riskAssessment.residualRisk.toUpperCase()}
                    </Tag>
                  </div>
                  <div>
                    <Text strong>Next Review: </Text>
                    <Text>{selectedUseCase.riskAssessment.nextReviewDate}</Text>
                  </div>
                </Card>

                <Card title="Stakeholders">
                  <List
                    dataSource={selectedUseCase.stakeholders}
                    renderItem={stakeholder => (
                      <List.Item>
                        <TeamOutlined style={{ marginRight: '8px', color: '#86868b' }} />
                        {stakeholder}
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default UseCases;
