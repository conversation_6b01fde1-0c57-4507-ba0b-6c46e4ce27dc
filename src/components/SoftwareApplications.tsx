import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Row,
  Col,
  Divider,
  List,
  Badge,
  Tooltip,
  Progress,
  Descriptions,
  Timeline,
  Alert
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  AppstoreOutlined,
  DatabaseOutlined,
  ApiOutlined,
  ShareAltOutlined,
  SafetyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { SoftwareApplication, UseCase, Jurisdiction } from '../types';
import { mockSoftwareApplications, mockUseCases, mockJurisdictions } from '../data/governanceMockData';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface SoftwareApplicationsProps {
  applications?: SoftwareApplication[];
  useCases?: UseCase[];
  jurisdictions?: Jurisdiction[];
  onAddApplication?: (application: SoftwareApplication) => void;
  onEditApplication?: (application: SoftwareApplication) => void;
}

const SoftwareApplications: React.FC<SoftwareApplicationsProps> = ({
  applications = mockSoftwareApplications,
  useCases = mockUseCases,
  jurisdictions = mockJurisdictions,
  onAddApplication,
  onEditApplication
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [editingApplication, setEditingApplication] = useState<SoftwareApplication | null>(null);
  const [selectedApplication, setSelectedApplication] = useState<SoftwareApplication | null>(null);
  const [form] = Form.useForm();

  const handleAdd = () => {
    setEditingApplication(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (application: SoftwareApplication) => {
    setEditingApplication(application);
    form.setFieldsValue({
      name: application.name,
      description: application.description,
      businessPurpose: application.businessPurpose,
      owner: application.owner,
      technicalContact: application.technicalContact,
      useCaseId: application.useCaseId,
      jurisdictions: application.jurisdictions,
      complianceStatus: application.complianceStatus
    });
    setIsModalVisible(true);
  };

  const handleView = (application: SoftwareApplication) => {
    setSelectedApplication(application);
    setIsDetailModalVisible(true);
  };

  const handleSubmit = async (values: any) => {
    const application: SoftwareApplication = {
      id: editingApplication?.id || `app-${Date.now()}`,
      name: values.name,
      description: values.description,
      businessPurpose: values.businessPurpose,
      owner: values.owner,
      technicalContact: values.technicalContact,
      dataIngressPoints: editingApplication?.dataIngressPoints || [],
      dataProcessors: editingApplication?.dataProcessors || [],
      dataEgressPoints: editingApplication?.dataEgressPoints || [],
      datasets: editingApplication?.datasets || [],
      useCaseId: values.useCaseId,
      jurisdictions: values.jurisdictions,
      complianceStatus: values.complianceStatus,
      lastAuditDate: editingApplication?.lastAuditDate,
      nextAuditDate: editingApplication?.nextAuditDate,
      createdAt: editingApplication?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    if (editingApplication) {
      onEditApplication?.(application);
    } else {
      onAddApplication?.(application);
    }

    setIsModalVisible(false);
    form.resetFields();
  };

  const getComplianceStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'success';
      case 'non-compliant': return 'error';
      case 'under-review': return 'warning';
      case 'requires-attention': return 'error';
      default: return 'default';
    }
  };

  const getComplianceStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <CheckCircleOutlined />;
      case 'non-compliant': return <ExclamationCircleOutlined />;
      case 'under-review': return <ClockCircleOutlined />;
      case 'requires-attention': return <ExclamationCircleOutlined />;
      default: return null;
    }
  };

  const columns = [
    {
      title: 'Application Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: SoftwareApplication) => (
        <div>
          <Text strong style={{ color: '#1d1d1f', fontSize: '16px' }}>{text}</Text>
          <br />
          <Text style={{ color: '#86868b', fontSize: '14px' }}>{record.businessPurpose}</Text>
        </div>
      ),
    },
    {
      title: 'Use Case',
      dataIndex: 'useCaseId',
      key: 'useCaseId',
      render: (useCaseId: string) => {
        const useCase = useCases.find(uc => uc.id === useCaseId);
        return (
          <Tag color="blue" style={{ borderRadius: '6px' }}>
            {useCase?.name || 'Unknown'}
          </Tag>
        );
      },
    },
    {
      title: 'Data Flow',
      key: 'dataFlow',
      render: (record: SoftwareApplication) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Tooltip title={`${record.dataIngressPoints.length} ingress points`}>
            <Badge count={record.dataIngressPoints.length} size="small">
              <ApiOutlined style={{ color: '#007AFF', fontSize: '16px' }} />
            </Badge>
          </Tooltip>
          <Text style={{ color: '#86868b' }}>→</Text>
          <Tooltip title={`${record.dataProcessors.length} processors`}>
            <Badge count={record.dataProcessors.length} size="small">
              <DatabaseOutlined style={{ color: '#FF9500', fontSize: '16px' }} />
            </Badge>
          </Tooltip>
          <Text style={{ color: '#86868b' }}>→</Text>
          <Tooltip title={`${record.dataEgressPoints.length} egress points`}>
            <Badge count={record.dataEgressPoints.length} size="small">
              <ShareAltOutlined style={{ color: '#34C759', fontSize: '16px' }} />
            </Badge>
          </Tooltip>
        </div>
      ),
    },
    {
      title: 'Jurisdictions',
      dataIndex: 'jurisdictions',
      key: 'jurisdictions',
      render: (jurisdictionIds: string[]) => (
        <div>
          {jurisdictionIds.map(id => {
            const jurisdiction = jurisdictions.find(j => j.id === id);
            return (
              <Tag key={id} style={{ borderRadius: '6px', marginBottom: '4px' }}>
                {jurisdiction?.code || id}
              </Tag>
            );
          })}
        </div>
      ),
    },
    {
      title: 'Compliance Status',
      dataIndex: 'complianceStatus',
      key: 'complianceStatus',
      render: (status: string) => (
        <Tag 
          color={getComplianceStatusColor(status)} 
          icon={getComplianceStatusIcon(status)}
          style={{ borderRadius: '6px', fontWeight: 500 }}
        >
          {status.replace('-', ' ').toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Owner',
      dataIndex: 'owner',
      key: 'owner',
      render: (owner: string) => (
        <Text style={{ color: '#1d1d1f', fontSize: '14px' }}>{owner}</Text>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: SoftwareApplication) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            style={{ color: '#007AFF' }}
          >
            View
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            style={{ color: '#007AFF' }}
          >
            Edit
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '0 4px' }}>
      <div style={{ marginBottom: '32px' }}>
        <Title level={1} style={{
          color: '#1d1d1f',
          fontSize: '34px',
          fontWeight: 700,
          margin: 0,
          marginBottom: '8px',
          letterSpacing: '-0.5px'
        }}>
          Software Applications
        </Title>
        <Text style={{
          color: '#86868b',
          fontSize: '17px',
          fontWeight: 400,
          lineHeight: '22px'
        }}>
          Register and manage software applications with comprehensive data flow documentation
        </Text>
      </div>

      <div style={{ marginBottom: '24px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          size="large"
          style={{
            borderRadius: '12px',
            height: '48px',
            fontSize: '16px',
            fontWeight: 500,
            boxShadow: '0 4px 12px rgba(0, 122, 255, 0.3)'
          }}
        >
          Register New Application
        </Button>
      </div>

      <Card
        style={{
          borderRadius: '16px',
          border: '1px solid #e5e5e7',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
        }}
      >
        <Table
          columns={columns}
          dataSource={applications}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} applications`,
          }}
          style={{ marginTop: '16px' }}
        />
      </Card>

      {/* Add/Edit Modal */}
      <Modal
        title={editingApplication ? 'Edit Software Application' : 'Register New Software Application'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ marginTop: '24px' }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Application Name"
                rules={[{ required: true, message: 'Please enter application name' }]}
              >
                <Input placeholder="Customer Web Application" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="useCaseId"
                label="Use Case"
                rules={[{ required: true, message: 'Please select use case' }]}
              >
                <Select placeholder="Select use case">
                  {useCases.map(useCase => (
                    <Option key={useCase.id} value={useCase.id}>
                      {useCase.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea rows={3} placeholder="Describe the application's functionality and purpose" />
          </Form.Item>

          <Form.Item
            name="businessPurpose"
            label="Business Purpose"
            rules={[{ required: true, message: 'Please enter business purpose' }]}
          >
            <TextArea rows={2} placeholder="Explain the business justification for this application" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="owner"
                label="Business Owner"
                rules={[{ required: true, message: 'Please enter business owner' }]}
              >
                <Input placeholder="Sarah Johnson, VP of Digital Commerce" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="technicalContact"
                label="Technical Contact"
                rules={[{ required: true, message: 'Please enter technical contact' }]}
              >
                <Input placeholder="Alex Rodriguez, Lead Developer" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="jurisdictions"
                label="Applicable Jurisdictions"
                rules={[{ required: true, message: 'Please select jurisdictions' }]}
              >
                <Select mode="multiple" placeholder="Select jurisdictions">
                  {jurisdictions.map(jurisdiction => (
                    <Option key={jurisdiction.id} value={jurisdiction.id}>
                      {jurisdiction.name} ({jurisdiction.code})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="complianceStatus"
                label="Compliance Status"
                rules={[{ required: true, message: 'Please select compliance status' }]}
              >
                <Select placeholder="Select compliance status">
                  <Option value="compliant">Compliant</Option>
                  <Option value="non-compliant">Non-Compliant</Option>
                  <Option value="under-review">Under Review</Option>
                  <Option value="requires-attention">Requires Attention</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'right', marginTop: '24px' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {editingApplication ? 'Update Application' : 'Register Application'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default SoftwareApplications;
