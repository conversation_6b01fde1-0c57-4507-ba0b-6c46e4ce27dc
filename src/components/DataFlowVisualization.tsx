import React, { useState } from 'react';
import {
  Card,
  Typography,
  Select,
  Row,
  Col,
  Tag,
  Space,
  Divider,
  Timeline,
  Badge,
  Tooltip,
  Alert
} from 'antd';
import {
  ApiOutlined,
  DatabaseOutlined,
  ShareAltOutlined,
  ArrowRightOutlined,
  SafetyOutlined,
  GlobalOutlined,
  LockOutlined
} from '@ant-design/icons';
import { SoftwareApplication, UseCase, Jurisdiction } from '../types';
import { mockSoftwareApplications, mockUseCases, mockJurisdictions } from '../data/governanceMockData';

const { Title, Text } = Typography;
const { Option } = Select;

interface DataFlowVisualizationProps {
  applications?: SoftwareApplication[];
  useCases?: UseCase[];
  jurisdictions?: Jurisdiction[];
}

const DataFlowVisualization: React.FC<DataFlowVisualizationProps> = ({
  applications = mockSoftwareApplications,
  useCases = mockUseCases,
  jurisdictions = mockJurisdictions
}) => {
  const [selectedApplicationId, setSelectedApplicationId] = useState<string>(applications[0]?.id || '');

  const selectedApplication = applications.find(app => app.id === selectedApplicationId);
  const selectedUseCase = useCases.find(uc => uc.id === selectedApplication?.useCaseId);

  const getDataFlowSteps = (application: SoftwareApplication) => {
    const steps = [];

    // Ingress points
    application.dataIngressPoints.forEach((ingress, index) => {
      steps.push({
        color: 'blue',
        children: (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
              <ApiOutlined style={{ color: '#007AFF' }} />
              <Text strong>{ingress.name}</Text>
              <Tag color="blue" size="small">{ingress.type}</Tag>
            </div>
            <Text style={{ fontSize: '12px', color: '#86868b' }}>{ingress.description}</Text>
            <div style={{ marginTop: '4px' }}>
              <Text style={{ fontSize: '11px', color: '#86868b' }}>
                Format: {ingress.dataFormat} | Frequency: {ingress.frequency}
              </Text>
            </div>
          </div>
        )
      });
    });

    // Processing steps
    application.dataProcessors.forEach((processor, index) => {
      steps.push({
        color: 'orange',
        children: (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
              <DatabaseOutlined style={{ color: '#FF9500' }} />
              <Text strong>{processor.name}</Text>
              <Tag color="orange" size="small">{processor.type}</Tag>
            </div>
            <Text style={{ fontSize: '12px', color: '#86868b' }}>{processor.description}</Text>
            <div style={{ marginTop: '4px' }}>
              <Space size="small">
                <Tag size="small" color={processor.privacyImpact === 'anonymizes' ? 'green' : 'default'}>
                  {processor.privacyImpact}
                </Tag>
                <Tag size="small" color={processor.automatedProcessing ? 'blue' : 'default'}>
                  {processor.automatedProcessing ? 'Automated' : 'Manual'}
                </Tag>
              </Space>
            </div>
          </div>
        )
      });
    });

    // Egress points
    application.dataEgressPoints.forEach((egress, index) => {
      steps.push({
        color: egress.internationalTransfer ? 'red' : 'green',
        children: (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
              <ShareAltOutlined style={{ color: egress.internationalTransfer ? '#FF3B30' : '#34C759' }} />
              <Text strong>{egress.name}</Text>
              <Tag color={egress.internationalTransfer ? 'red' : 'green'} size="small">
                {egress.type}
              </Tag>
            </div>
            <Text style={{ fontSize: '12px', color: '#86868b' }}>{egress.description}</Text>
            <div style={{ marginTop: '4px' }}>
              <Text style={{ fontSize: '11px', color: '#86868b' }}>
                Recipients: {egress.recipients.join(', ')}
              </Text>
              {egress.internationalTransfer && (
                <div style={{ marginTop: '2px' }}>
                  <Tag color="red" size="small" icon={<GlobalOutlined />}>
                    International Transfer
                  </Tag>
                </div>
              )}
            </div>
          </div>
        )
      });
    });

    return steps;
  };

  const getComplianceColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'success';
      case 'non-compliant': return 'error';
      case 'under-review': return 'warning';
      case 'requires-attention': return 'error';
      default: return 'default';
    }
  };

  return (
    <div style={{ padding: '0 4px' }}>
      <div style={{ marginBottom: '32px' }}>
        <Title level={1} style={{
          color: '#1d1d1f',
          fontSize: '34px',
          fontWeight: 700,
          margin: 0,
          marginBottom: '8px',
          letterSpacing: '-0.5px'
        }}>
          Data Flow Visualization
        </Title>
        <Text style={{
          color: '#86868b',
          fontSize: '17px',
          fontWeight: 400,
          lineHeight: '22px'
        }}>
          Visualize complete data flows from ingress through processing to egress points
        </Text>
      </div>

      <div style={{ marginBottom: '24px' }}>
        <Select
          value={selectedApplicationId}
          onChange={setSelectedApplicationId}
          style={{ width: 400 }}
          size="large"
          placeholder="Select an application to visualize"
        >
          {applications.map(app => (
            <Option key={app.id} value={app.id}>
              {app.name}
            </Option>
          ))}
        </Select>
      </div>

      {selectedApplication && (
        <Row gutter={24}>
          <Col span={16}>
            <Card
              title="Data Flow Pipeline"
              style={{
                borderRadius: '16px',
                border: '1px solid #e5e5e7',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                marginBottom: '24px'
              }}
            >
              <Timeline
                items={getDataFlowSteps(selectedApplication)}
                style={{ marginTop: '16px' }}
              />
            </Card>
          </Col>

          <Col span={8}>
            <Card
              title="Application Overview"
              style={{
                borderRadius: '16px',
                border: '1px solid #e5e5e7',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                marginBottom: '24px'
              }}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div>
                  <Text strong style={{ color: '#1d1d1f' }}>Business Purpose</Text>
                  <br />
                  <Text style={{ color: '#86868b', fontSize: '14px' }}>
                    {selectedApplication.businessPurpose}
                  </Text>
                </div>

                <div>
                  <Text strong style={{ color: '#1d1d1f' }}>Owner</Text>
                  <br />
                  <Text style={{ color: '#86868b', fontSize: '14px' }}>
                    {selectedApplication.owner}
                  </Text>
                </div>

                <div>
                  <Text strong style={{ color: '#1d1d1f' }}>Compliance Status</Text>
                  <br />
                  <Tag 
                    color={getComplianceColor(selectedApplication.complianceStatus)}
                    style={{ borderRadius: '6px', fontWeight: 500 }}
                  >
                    {selectedApplication.complianceStatus.replace('-', ' ').toUpperCase()}
                  </Tag>
                </div>

                <div>
                  <Text strong style={{ color: '#1d1d1f' }}>Jurisdictions</Text>
                  <br />
                  <Space wrap>
                    {selectedApplication.jurisdictions.map(jurisdictionId => {
                      const jurisdiction = jurisdictions.find(j => j.id === jurisdictionId);
                      return (
                        <Tag key={jurisdictionId} style={{ borderRadius: '6px' }}>
                          {jurisdiction?.name || jurisdictionId}
                        </Tag>
                      );
                    })}
                  </Space>
                </div>
              </Space>
            </Card>

            <Card
              title="Flow Statistics"
              style={{
                borderRadius: '16px',
                border: '1px solid #e5e5e7',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
              }}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>Ingress Points</Text>
                  <Badge count={selectedApplication.dataIngressPoints.length} style={{ backgroundColor: '#007AFF' }} />
                </div>

                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>Processing Steps</Text>
                  <Badge count={selectedApplication.dataProcessors.length} style={{ backgroundColor: '#FF9500' }} />
                </div>

                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>Egress Points</Text>
                  <Badge count={selectedApplication.dataEgressPoints.length} style={{ backgroundColor: '#34C759' }} />
                </div>

                <Divider style={{ margin: '12px 0' }} />

                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>International Transfers</Text>
                  <Badge 
                    count={selectedApplication.dataEgressPoints.filter(e => e.internationalTransfer).length} 
                    style={{ backgroundColor: '#FF3B30' }} 
                  />
                </div>

                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>Automated Processing</Text>
                  <Badge 
                    count={selectedApplication.dataProcessors.filter(p => p.automatedProcessing).length} 
                    style={{ backgroundColor: '#007AFF' }} 
                  />
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      )}

      {selectedApplication && selectedUseCase && (
        <Card
          title="Use Case Context"
          style={{
            borderRadius: '16px',
            border: '1px solid #e5e5e7',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
          }}
        >
          <Row gutter={24}>
            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <Text strong style={{ color: '#1d1d1f' }}>Business Justification</Text>
                <br />
                <Text style={{ color: '#86868b', fontSize: '14px' }}>
                  {selectedUseCase.businessJustification}
                </Text>
              </div>
              <div>
                <Text strong style={{ color: '#1d1d1f' }}>Legal Basis</Text>
                <br />
                <Tag color="blue" style={{ borderRadius: '6px' }}>
                  {selectedUseCase.legalBasis}
                </Tag>
              </div>
            </Col>
            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <Text strong style={{ color: '#1d1d1f' }}>Data Subjects</Text>
                <br />
                <Space wrap>
                  {selectedUseCase.dataSubjects.map(subject => (
                    <Tag key={subject} style={{ borderRadius: '6px' }}>
                      {subject}
                    </Tag>
                  ))}
                </Space>
              </div>
              <div>
                <Text strong style={{ color: '#1d1d1f' }}>Risk Level</Text>
                <br />
                <Tag 
                  color={selectedUseCase.riskAssessment.overallRisk === 'high' ? 'red' : 
                        selectedUseCase.riskAssessment.overallRisk === 'medium' ? 'orange' : 'green'}
                  style={{ borderRadius: '6px', fontWeight: 500 }}
                >
                  {selectedUseCase.riskAssessment.overallRisk.toUpperCase()}
                </Tag>
              </div>
            </Col>
          </Row>
        </Card>
      )}

      {selectedApplication && selectedApplication.dataEgressPoints.some(e => e.internationalTransfer) && (
        <Alert
          message="International Data Transfers Detected"
          description="This application includes data transfers to international recipients. Ensure appropriate safeguards are in place."
          type="warning"
          showIcon
          style={{ marginTop: '24px', borderRadius: '12px' }}
        />
      )}
    </div>
  );
};

export default DataFlowVisualization;
