import React from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Typography,
  Table,
  Tag,
  Space,
  Alert,
  Timeline,
  Badge,
  Tooltip
} from 'antd';
import {
  TrophyOutlined,
  SafetyOutlined,
  DatabaseOutlined,
  AppstoreOutlined,
  GlobalOutlined,
  ExclamationTriangleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  RiseOutlined,
  FallOutlined,
  MinusOutlined
} from '@ant-design/icons';
import {
  Dataset,
  PrivacyReview
} from '../types';

// Define governance types locally to avoid circular dependency
type SoftwareApplication = any;
type UseCase = any;
type Jurisdiction = any;
type ApprovalRecord = any;
type AuditRecord = any;
import {
  mockDatasets,
  mockPrivacyReviews
} from '../data/mockData';
import {
  mockSoftwareApplications,
  mockUseCases,
  mockJurisdictions,
  mockApprovalRecords,
  mockAuditRecords
} from '../data/governanceMockData';

const { Title, Text } = Typography;

interface ExecutiveDashboardProps {
  applications?: SoftwareApplication[];
  useCases?: UseCase[];
  datasets?: Dataset[];
  reviews?: PrivacyReview[];
  jurisdictions?: Jurisdiction[];
  approvalRecords?: ApprovalRecord[];
  auditRecords?: AuditRecord[];
}

const ExecutiveDashboard: React.FC<ExecutiveDashboardProps> = ({
  applications = mockSoftwareApplications,
  useCases = mockUseCases,
  datasets = mockDatasets,
  reviews = mockPrivacyReviews,
  jurisdictions = mockJurisdictions,
  approvalRecords = mockApprovalRecords,
  auditRecords = mockAuditRecords
}) => {
  // Calculate key metrics
  const totalApplications = applications.length;
  const compliantApplications = applications.filter(app => app.complianceStatus === 'compliant').length;
  const complianceScore = totalApplications > 0 ? Math.round((compliantApplications / totalApplications) * 100) : 0;

  const totalUseCases = useCases.length;
  const approvedUseCases = approvalRecords.filter(ar => ar.finalApprovalStatus === 'approved').length;
  const approvalRate = totalUseCases > 0 ? Math.round((approvedUseCases / totalUseCases) * 100) : 0;

  const highRiskUseCases = useCases.filter(uc =>
    uc.riskAssessment.overallRisk === 'high' || uc.riskAssessment.overallRisk === 'very-high'
  ).length;

  const pendingReviews = reviews.filter(r => r.status === 'pending' || r.status === 'in-progress').length;

  const jurisdictionCoverage = jurisdictions.length;

  // Recent activity
  const recentAudits = auditRecords
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 5);

  // Risk distribution
  const riskDistribution = {
    low: useCases.filter(uc => uc.riskAssessment.overallRisk === 'low').length,
    medium: useCases.filter(uc => uc.riskAssessment.overallRisk === 'medium').length,
    high: useCases.filter(uc => uc.riskAssessment.overallRisk === 'high').length,
    veryHigh: useCases.filter(uc => uc.riskAssessment.overallRisk === 'very-high').length
  };

  // Compliance by jurisdiction
  const jurisdictionCompliance = jurisdictions.map(jurisdiction => {
    const jurisdictionApps = applications.filter(app => app.jurisdictions.includes(jurisdiction.id));
    const compliantApps = jurisdictionApps.filter(app => app.complianceStatus === 'compliant');
    return {
      jurisdiction: jurisdiction.name,
      code: jurisdiction.code,
      total: jurisdictionApps.length,
      compliant: compliantApps.length,
      compliance: jurisdictionApps.length > 0 ? Math.round((compliantApps.length / jurisdictionApps.length) * 100) : 0
    };
  });

  const getTrendIcon = (value: number) => {
    if (value > 0) return <RiseOutlined style={{ color: '#52c41a' }} />;
    if (value < 0) return <FallOutlined style={{ color: '#ff4d4f' }} />;
    return <MinusOutlined style={{ color: '#8c8c8c' }} />;
  };

  const getComplianceColor = (score: number) => {
    if (score >= 90) return '#52c41a';
    if (score >= 70) return '#faad14';
    return '#ff4d4f';
  };

  const jurisdictionColumns = [
    {
      title: 'Jurisdiction',
      key: 'jurisdiction',
      render: (record: any) => (
        <div>
          <Text strong>{record.jurisdiction}</Text>
          <br />
          <Text style={{ color: '#86868b', fontSize: '12px' }}>{record.code}</Text>
        </div>
      ),
    },
    {
      title: 'Applications',
      dataIndex: 'total',
      key: 'total',
      render: (total: number) => (
        <Badge count={total} style={{ backgroundColor: '#007AFF' }} />
      ),
    },
    {
      title: 'Compliance Rate',
      key: 'compliance',
      render: (record: any) => (
        <div>
          <Progress
            percent={record.compliance}
            size="small"
            strokeColor={getComplianceColor(record.compliance)}
            showInfo={false}
            style={{ marginBottom: '4px' }}
          />
          <Text style={{ fontSize: '12px', color: getComplianceColor(record.compliance) }}>
            {record.compliance}% ({record.compliant}/{record.total})
          </Text>
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: '0 4px' }}>
      <div style={{ marginBottom: '32px' }}>
        <Title level={1} style={{
          color: '#1d1d1f',
          fontSize: '34px',
          fontWeight: 700,
          margin: 0,
          marginBottom: '8px',
          letterSpacing: '-0.5px'
        }}>
          Executive Dashboard
        </Title>
        <Text style={{
          color: '#86868b',
          fontSize: '17px',
          fontWeight: 400,
          lineHeight: '22px'
        }}>
          High-level compliance overview and governance metrics for leadership reporting
        </Text>
      </div>

      {/* Key Performance Indicators */}
      <Row gutter={24} style={{ marginBottom: '32px' }}>
        <Col span={6}>
          <Card style={{ borderRadius: '16px', border: '1px solid #e5e5e7' }}>
            <Statistic
              title="Overall Compliance Score"
              value={complianceScore}
              suffix="%"
              prefix={<TrophyOutlined style={{ color: getComplianceColor(complianceScore) }} />}
              valueStyle={{ color: getComplianceColor(complianceScore), fontSize: '32px', fontWeight: 700 }}
            />
            <div style={{ marginTop: '8px', display: 'flex', alignItems: 'center', gap: '4px' }}>
              {getTrendIcon(5)}
              <Text style={{ color: '#86868b', fontSize: '12px' }}>+5% from last quarter</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ borderRadius: '16px', border: '1px solid #e5e5e7' }}>
            <Statistic
              title="Use Case Approval Rate"
              value={approvalRate}
              suffix="%"
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '32px', fontWeight: 700 }}
            />
            <div style={{ marginTop: '8px', display: 'flex', alignItems: 'center', gap: '4px' }}>
              {getTrendIcon(0)}
              <Text style={{ color: '#86868b', fontSize: '12px' }}>No change from last quarter</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ borderRadius: '16px', border: '1px solid #e5e5e7' }}>
            <Statistic
              title="High Risk Use Cases"
              value={highRiskUseCases}
              prefix={<ExclamationTriangleOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f', fontSize: '32px', fontWeight: 700 }}
            />
            <div style={{ marginTop: '8px', display: 'flex', alignItems: 'center', gap: '4px' }}>
              {getTrendIcon(-2)}
              <Text style={{ color: '#86868b', fontSize: '12px' }}>-2 from last quarter</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ borderRadius: '16px', border: '1px solid #e5e5e7' }}>
            <Statistic
              title="Pending Reviews"
              value={pendingReviews}
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14', fontSize: '32px', fontWeight: 700 }}
            />
            <div style={{ marginTop: '8px', display: 'flex', alignItems: 'center', gap: '4px' }}>
              {getTrendIcon(1)}
              <Text style={{ color: '#86868b', fontSize: '12px' }}>+1 from last week</Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Portfolio Overview */}
      <Row gutter={24} style={{ marginBottom: '32px' }}>
        <Col span={8}>
          <Card
            title="Data Governance Portfolio"
            style={{ borderRadius: '16px', border: '1px solid #e5e5e7', height: '300px' }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="Use Cases"
                  value={totalUseCases}
                  prefix={<AppstoreOutlined style={{ color: '#007AFF' }} />}
                  valueStyle={{ fontSize: '24px', fontWeight: 600 }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Applications"
                  value={totalApplications}
                  prefix={<DatabaseOutlined style={{ color: '#007AFF' }} />}
                  valueStyle={{ fontSize: '24px', fontWeight: 600 }}
                />
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: '24px' }}>
              <Col span={12}>
                <Statistic
                  title="Datasets"
                  value={datasets.length}
                  prefix={<DatabaseOutlined style={{ color: '#007AFF' }} />}
                  valueStyle={{ fontSize: '24px', fontWeight: 600 }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Jurisdictions"
                  value={jurisdictionCoverage}
                  prefix={<GlobalOutlined style={{ color: '#007AFF' }} />}
                  valueStyle={{ fontSize: '24px', fontWeight: 600 }}
                />
              </Col>
            </Row>
          </Card>
        </Col>

        <Col span={8}>
          <Card
            title="Risk Distribution"
            style={{ borderRadius: '16px', border: '1px solid #e5e5e7', height: '300px' }}
          >
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text>Low Risk</Text>
                <Text strong>{riskDistribution.low}</Text>
              </div>
              <Progress percent={(riskDistribution.low / totalUseCases) * 100} strokeColor="#52c41a" showInfo={false} />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text>Medium Risk</Text>
                <Text strong>{riskDistribution.medium}</Text>
              </div>
              <Progress percent={(riskDistribution.medium / totalUseCases) * 100} strokeColor="#faad14" showInfo={false} />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text>High Risk</Text>
                <Text strong>{riskDistribution.high}</Text>
              </div>
              <Progress percent={(riskDistribution.high / totalUseCases) * 100} strokeColor="#ff7a45" showInfo={false} />
            </div>

            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text>Very High Risk</Text>
                <Text strong>{riskDistribution.veryHigh}</Text>
              </div>
              <Progress percent={(riskDistribution.veryHigh / totalUseCases) * 100} strokeColor="#ff4d4f" showInfo={false} />
            </div>
          </Card>
        </Col>

        <Col span={8}>
          <Card
            title="Recent Audit Activity"
            style={{ borderRadius: '16px', border: '1px solid #e5e5e7', height: '300px' }}
          >
            <Timeline
              size="small"
              items={recentAudits.map(audit => ({
                color: audit.action === 'approved' ? 'green' :
                       audit.action === 'rejected' ? 'red' : 'blue',
                children: (
                  <div>
                    <Text strong style={{ fontSize: '12px' }}>
                      {audit.action.toUpperCase()}
                    </Text>
                    <br />
                    <Text style={{ fontSize: '11px', color: '#86868b' }}>
                      {audit.actor}
                    </Text>
                    <br />
                    <Text style={{ fontSize: '10px', color: '#86868b' }}>
                      {new Date(audit.timestamp).toLocaleDateString()}
                    </Text>
                  </div>
                )
              }))}
            />
          </Card>
        </Col>
      </Row>

      {/* Jurisdiction Compliance */}
      <Row gutter={24}>
        <Col span={24}>
          <Card
            title="Compliance by Jurisdiction"
            style={{ borderRadius: '16px', border: '1px solid #e5e5e7' }}
          >
            <Table
              columns={jurisdictionColumns}
              dataSource={jurisdictionCompliance}
              rowKey="code"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      {/* Alerts */}
      {highRiskUseCases > 0 && (
        <Alert
          message="High Risk Use Cases Detected"
          description={`${highRiskUseCases} use cases require immediate attention due to high risk assessment. Review and implement additional safeguards.`}
          type="warning"
          showIcon
          style={{ marginTop: '24px', borderRadius: '12px' }}
        />
      )}
    </div>
  );
};

export default ExecutiveDashboard;
