import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Row,
  Col,
  Divider,
  Badge,
  Tooltip,
  Descriptions,
  Alert,
  Switch
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  DatabaseOutlined,
  CloudOutlined,
  ServerOutlined,
  ApiOutlined,
  FileOutlined,
  SafetyOutlined,
  LockOutlined,
  UnlockOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';
import { mockDataSources, mockDataSourceLocations, mockJurisdictions } from '../data/governanceMockData';

// Define types locally to avoid circular dependency
type DataSource = any;
type DataSourceLocation = any;
type Jurisdiction = any;

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface DataSourcesProps {
  dataSources?: DataSource[];
  locations?: DataSourceLocation[];
  jurisdictions?: Jurisdiction[];
  onAddDataSource?: (dataSource: DataSource) => void;
  onEditDataSource?: (dataSource: DataSource) => void;
}

const DataSources: React.FC<DataSourcesProps> = ({
  dataSources = mockDataSources,
  locations = mockDataSourceLocations,
  jurisdictions = mockJurisdictions,
  onAddDataSource,
  onEditDataSource
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [editingDataSource, setEditingDataSource] = useState<DataSource | null>(null);
  const [selectedDataSource, setSelectedDataSource] = useState<DataSource | null>(null);
  const [form] = Form.useForm();

  const handleAdd = () => {
    setEditingDataSource(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (dataSource: DataSource) => {
    setEditingDataSource(dataSource);
    form.setFieldsValue({
      name: dataSource.name,
      description: dataSource.description,
      type: dataSource.type,
      connectionString: dataSource.connectionString,
      locationId: dataSource.location.id,
      securityClassification: dataSource.securityClassification,
      encryptionStatus: dataSource.encryptionStatus,
      backupFrequency: dataSource.backupPolicy.frequency,
      backupRetentionPeriod: dataSource.backupPolicy.retentionPeriod,
      backupLocation: dataSource.backupPolicy.location,
      backupEncryptionRequired: dataSource.backupPolicy.encryptionRequired
    });
    setIsModalVisible(true);
  };

  const handleView = (dataSource: DataSource) => {
    setSelectedDataSource(dataSource);
    setIsDetailModalVisible(true);
  };

  const handleSubmit = async (values: any) => {
    const selectedLocation = locations.find(loc => loc.id === values.locationId);
    if (!selectedLocation) return;

    const dataSource: DataSource = {
      id: editingDataSource?.id || `ds-${Date.now()}`,
      name: values.name,
      description: values.description,
      type: values.type,
      connectionString: values.connectionString,
      location: selectedLocation,
      datasets: editingDataSource?.datasets || [],
      securityClassification: values.securityClassification,
      encryptionStatus: values.encryptionStatus,
      accessControls: editingDataSource?.accessControls || [],
      backupPolicy: {
        frequency: values.backupFrequency,
        retentionPeriod: values.backupRetentionPeriod,
        location: values.backupLocation,
        encryptionRequired: values.backupEncryptionRequired
      },
      createdAt: editingDataSource?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    if (editingDataSource) {
      onEditDataSource?.(dataSource);
    } else {
      onAddDataSource?.(dataSource);
    }

    setIsModalVisible(false);
    form.resetFields();
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'database': return <DatabaseOutlined />;
      case 'cloud-storage': return <CloudOutlined />;
      case 'file-system': return <FileOutlined />;
      case 'api': return <ApiOutlined />;
      case 'stream': return <ServerOutlined />;
      case 'data-lake': return <CloudOutlined />;
      default: return <DatabaseOutlined />;
    }
  };

  const getEncryptionColor = (status: string) => {
    switch (status) {
      case 'encrypted': return 'success';
      case 'partially-encrypted': return 'warning';
      case 'not-encrypted': return 'error';
      default: return 'default';
    }
  };

  const getEncryptionIcon = (status: string) => {
    return status === 'encrypted' ? <LockOutlined /> : <UnlockOutlined />;
  };

  const getClassificationColor = (classification: string) => {
    switch (classification) {
      case 'public': return 'default';
      case 'internal': return 'blue';
      case 'confidential': return 'orange';
      case 'restricted': return 'red';
      case 'highly-sensitive': return 'red';
      default: return 'default';
    }
  };

  const columns = [
    {
      title: 'Data Source',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: DataSource) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
            {getTypeIcon(record.type)}
            <Text strong style={{ color: '#1d1d1f', fontSize: '16px' }}>{text}</Text>
          </div>
          <Text style={{ color: '#86868b', fontSize: '14px' }}>{record.description}</Text>
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color="blue" style={{ borderRadius: '6px' }}>
          {type.replace('-', ' ').toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Location',
      key: 'location',
      render: (record: DataSource) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <EnvironmentOutlined style={{ color: '#007AFF' }} />
            <Text strong>{record.location.name}</Text>
          </div>
          <Text style={{ color: '#86868b', fontSize: '12px' }}>
            {record.location.country}, {record.location.region}
          </Text>
        </div>
      ),
    },
    {
      title: 'Security',
      key: 'security',
      render: (record: DataSource) => (
        <div>
          <Tag
            color={getClassificationColor(record.securityClassification)}
            style={{ borderRadius: '6px', marginBottom: '4px' }}
          >
            {record.securityClassification.replace('-', ' ').toUpperCase()}
          </Tag>
          <br />
          <Tag
            color={getEncryptionColor(record.encryptionStatus)}
            icon={getEncryptionIcon(record.encryptionStatus)}
            style={{ borderRadius: '6px' }}
          >
            {record.encryptionStatus.replace('-', ' ').toUpperCase()}
          </Tag>
        </div>
      ),
    },
    {
      title: 'Datasets',
      dataIndex: 'datasets',
      key: 'datasets',
      render: (datasets: string[]) => (
        <Badge count={datasets.length} style={{ backgroundColor: '#007AFF' }}>
          <DatabaseOutlined style={{ fontSize: '16px', color: '#86868b' }} />
        </Badge>
      ),
    },
    {
      title: 'Certifications',
      key: 'certifications',
      render: (record: DataSource) => (
        <div>
          {record.location.certifications.slice(0, 2).map(cert => (
            <Tag key={cert} style={{ borderRadius: '6px', marginBottom: '2px', fontSize: '11px' }}>
              {cert}
            </Tag>
          ))}
          {record.location.certifications.length > 2 && (
            <Text style={{ color: '#86868b', fontSize: '12px' }}>
              +{record.location.certifications.length - 2} more
            </Text>
          )}
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: DataSource) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            style={{ color: '#007AFF' }}
          >
            View
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            style={{ color: '#007AFF' }}
          >
            Edit
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '0 4px' }}>
      <div style={{ marginBottom: '32px' }}>
        <Title level={1} style={{
          color: '#1d1d1f',
          fontSize: '34px',
          fontWeight: 700,
          margin: 0,
          marginBottom: '8px',
          letterSpacing: '-0.5px'
        }}>
          Data Sources
        </Title>
        <Text style={{
          color: '#86868b',
          fontSize: '17px',
          fontWeight: 400,
          lineHeight: '22px'
        }}>
          Manage physical data storage locations and their security configurations
        </Text>
      </div>

      <div style={{ marginBottom: '24px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          size="large"
          style={{
            borderRadius: '12px',
            height: '48px',
            fontSize: '16px',
            fontWeight: 500,
            boxShadow: '0 4px 12px rgba(0, 122, 255, 0.3)'
          }}
        >
          Add New Data Source
        </Button>
      </div>

      <Card
        style={{
          borderRadius: '16px',
          border: '1px solid #e5e5e7',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
        }}
      >
        <Table
          columns={columns}
          dataSource={dataSources}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} data sources`,
          }}
          style={{ marginTop: '16px' }}
        />
      </Card>

      {/* Add/Edit Modal */}
      <Modal
        title={editingDataSource ? 'Edit Data Source' : 'Add New Data Source'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ marginTop: '24px' }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Data Source Name"
                rules={[{ required: true, message: 'Please enter data source name' }]}
              >
                <Input placeholder="Customer Database (Production)" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="Type"
                rules={[{ required: true, message: 'Please select type' }]}
              >
                <Select placeholder="Select data source type">
                  <Option value="database">Database</Option>
                  <Option value="file-system">File System</Option>
                  <Option value="api">API</Option>
                  <Option value="stream">Stream</Option>
                  <Option value="cloud-storage">Cloud Storage</Option>
                  <Option value="data-lake">Data Lake</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea rows={3} placeholder="Describe the data source and its purpose" />
          </Form.Item>

          <Form.Item
            name="connectionString"
            label="Connection String (Optional)"
          >
            <Input placeholder="postgresql://prod-db.internal:5432/customers" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="locationId"
                label="Location"
                rules={[{ required: true, message: 'Please select location' }]}
              >
                <Select placeholder="Select data source location">
                  {locations.map(location => (
                    <Option key={location.id} value={location.id}>
                      {location.name} ({location.country})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="securityClassification"
                label="Security Classification"
                rules={[{ required: true, message: 'Please select security classification' }]}
              >
                <Select placeholder="Select classification">
                  <Option value="public">Public</Option>
                  <Option value="internal">Internal</Option>
                  <Option value="confidential">Confidential</Option>
                  <Option value="restricted">Restricted</Option>
                  <Option value="highly-sensitive">Highly Sensitive</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="encryptionStatus"
            label="Encryption Status"
            rules={[{ required: true, message: 'Please select encryption status' }]}
          >
            <Select placeholder="Select encryption status">
              <Option value="encrypted">Encrypted</Option>
              <Option value="partially-encrypted">Partially Encrypted</Option>
              <Option value="not-encrypted">Not Encrypted</Option>
            </Select>
          </Form.Item>

          <Divider>Backup Policy</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="backupFrequency"
                label="Backup Frequency"
                rules={[{ required: true, message: 'Please select backup frequency' }]}
              >
                <Select placeholder="Select frequency">
                  <Option value="daily">Daily</Option>
                  <Option value="weekly">Weekly</Option>
                  <Option value="monthly">Monthly</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="backupRetentionPeriod"
                label="Retention Period (days)"
                rules={[{ required: true, message: 'Please enter retention period' }]}
              >
                <Input type="number" placeholder="90" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="backupLocation"
                label="Backup Location"
                rules={[{ required: true, message: 'Please select backup location' }]}
              >
                <Select placeholder="Select location">
                  <Option value="same-region">Same Region</Option>
                  <Option value="different-region">Different Region</Option>
                  <Option value="different-country">Different Country</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="backupEncryptionRequired"
            label="Backup Encryption Required"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: '24px' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {editingDataSource ? 'Update Data Source' : 'Add Data Source'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default DataSources;
