export interface Dataset {
  id: string;
  name: string;
  description: string;
  source: string;
  collectionDate: string;
  collectionFrequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually' | 'one-time';
  dataTypes: DataType[];
  schema: SchemaField[];
  consentStatus: ConsentStatus;
  privacyClassification: PrivacyClassification;
  retentionPolicy: RetentionPolicy;
  complianceStatus: ComplianceStatus;
  lastReviewDate?: string;
  nextReviewDate?: string;
  tags: string[];
  owner: string;
  createdAt: string;
  updatedAt: string;
}

export interface DataType {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array';
  isPII: boolean;
  isSensitive: boolean;
  description?: string;
}

export interface SchemaField {
  fieldName: string;
  dataType: string;
  isRequired: boolean;
  isPII: boolean;
  description?: string;
  constraints?: string[];
}

export interface ConsentStatus {
  hasConsent: boolean;
  consentType: 'explicit' | 'implicit' | 'legitimate-interest' | 'none';
  consentDate?: string;
  consentExpiry?: string;
  withdrawalMechanism: string;
  granularConsent: GranularConsent[];
}

export interface GranularConsent {
  purpose: string;
  hasConsent: boolean;
  consentDate?: string;
}

export type PrivacyClassification = 'public' | 'internal' | 'confidential' | 'restricted' | 'highly-sensitive';

export interface RetentionPolicy {
  retentionPeriod: number; // in days
  retentionReason: string;
  disposalMethod: 'delete' | 'anonymize' | 'archive';
  expirationDate: string;
  autoDelete: boolean;
}

export type ComplianceStatus = 'compliant' | 'non-compliant' | 'under-review' | 'requires-attention';

export interface PrivacyReview {
  id: string;
  datasetId: string;
  reviewType: 'initial' | 'periodic' | 'incident-driven' | 'regulatory';
  reviewDate: string;
  reviewer: string;
  status: 'pending' | 'in-progress' | 'completed' | 'requires-action';
  privacyImpactAssessment: PrivacyImpactAssessment;
  findings: ReviewFinding[];
  recommendations: string[];
  nextReviewDate?: string;
  approvalStatus: 'pending' | 'approved' | 'rejected';
  approver?: string;
  approvalDate?: string;
}

export interface PrivacyImpactAssessment {
  dataProcessingPurpose: string;
  legalBasis: string;
  dataSubjects: string[];
  dataCategories: string[];
  recipients: string[];
  internationalTransfers: boolean;
  transferMechanisms?: string[];
  retentionJustification: string;
  riskLevel: 'low' | 'medium' | 'high' | 'very-high';
  riskMitigations: string[];
  dpoConsultation: boolean;
  publicConsultation: boolean;
}

export interface ReviewFinding {
  id: string;
  category: 'data-minimization' | 'consent' | 'security' | 'retention' | 'transparency' | 'rights';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  recommendation: string;
  status: 'open' | 'in-progress' | 'resolved' | 'accepted-risk';
  dueDate?: string;
  assignee?: string;
}

export interface ComplianceReport {
  id: string;
  title: string;
  type: 'gdpr' | 'ccpa' | 'hipaa' | 'sox' | 'custom';
  generatedDate: string;
  reportPeriod: {
    startDate: string;
    endDate: string;
  };
  datasets: string[]; // dataset IDs
  metrics: ComplianceMetric[];
  summary: string;
  recommendations: string[];
  status: 'draft' | 'final' | 'submitted';
}

export interface ComplianceMetric {
  name: string;
  value: number;
  unit: string;
  target?: number;
  status: 'good' | 'warning' | 'critical';
  trend: 'improving' | 'stable' | 'declining';
}

export interface FilterOptions {
  privacyClassification?: PrivacyClassification[];
  complianceStatus?: ComplianceStatus[];
  dataTypes?: string[];
  consentStatus?: string[];
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  tags?: string[];
  owner?: string[];
}

export interface DashboardMetrics {
  totalDatasets: number;
  compliantDatasets: number;
  pendingReviews: number;
  expiringSoon: number;
  highRiskDatasets: number;
  consentWithdrawals: number;
  dataBreaches: number;
  complianceScore: number;
}

// New Data Governance Entities

export interface SoftwareApplication {
  id: string;
  name: string;
  description: string;
  businessPurpose: string;
  owner: string;
  technicalContact: string;
  dataIngressPoints: DataIngressPoint[];
  dataProcessors: DataProcessor[];
  dataEgressPoints: DataEgressPoint[];
  datasets: string[]; // dataset IDs
  useCaseId: string;
  jurisdictions: string[]; // jurisdiction IDs
  complianceStatus: ComplianceStatus;
  lastAuditDate?: string;
  nextAuditDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface DataSource {
  id: string;
  name: string;
  description: string;
  type: 'database' | 'file-system' | 'api' | 'stream' | 'cloud-storage' | 'data-lake';
  connectionString?: string;
  location: DataSourceLocation;
  datasets: string[]; // dataset IDs stored in this source
  securityClassification: PrivacyClassification;
  encryptionStatus: 'encrypted' | 'partially-encrypted' | 'not-encrypted';
  accessControls: AccessControl[];
  backupPolicy: BackupPolicy;
  createdAt: string;
  updatedAt: string;
}

export interface DataSourceLocation {
  id: string;
  name: string;
  country: string;
  region: string;
  dataCenter?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  jurisdictionId: string;
  certifications: string[]; // ISO 27001, SOC 2, etc.
  residencyRequirements: string[];
}

export interface DataIngressPoint {
  id: string;
  name: string;
  type: 'api' | 'file-upload' | 'database-sync' | 'stream' | 'manual-entry' | 'third-party';
  description: string;
  sourceSystem?: string;
  dataFormat: 'json' | 'xml' | 'csv' | 'binary' | 'structured' | 'unstructured';
  frequency: 'real-time' | 'batch' | 'scheduled' | 'on-demand';
  volumeEstimate?: string;
  securityMeasures: string[];
  monitoringEnabled: boolean;
}

export interface DataProcessor {
  id: string;
  name: string;
  type: 'transformation' | 'enrichment' | 'validation' | 'anonymization' | 'aggregation' | 'analysis';
  description: string;
  processingLogic: string;
  inputDataTypes: string[];
  outputDataTypes: string[];
  retentionImpact: 'none' | 'extends' | 'reduces' | 'transforms';
  privacyImpact: 'none' | 'anonymizes' | 'pseudonymizes' | 'enriches' | 'exposes';
  automatedProcessing: boolean;
  humanReviewRequired: boolean;
}

export interface DataEgressPoint {
  id: string;
  name: string;
  type: 'api' | 'file-export' | 'database-sync' | 'report' | 'third-party-sharing' | 'analytics';
  description: string;
  destinationSystem?: string;
  recipients: string[];
  dataFormat: 'json' | 'xml' | 'csv' | 'pdf' | 'dashboard' | 'api-response';
  frequency: 'real-time' | 'batch' | 'scheduled' | 'on-demand';
  securityMeasures: string[];
  internationalTransfer: boolean;
  transferMechanisms?: string[];
}

export interface UseCase {
  id: string;
  name: string;
  description: string;
  businessJustification: string;
  legalBasis: string;
  dataSubjects: string[];
  applications: string[]; // application IDs
  approvalRecord?: ApprovalRecord;
  auditRecords: AuditRecord[];
  riskAssessment: RiskAssessment;
  owner: string;
  stakeholders: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Policy {
  id: string;
  name: string;
  description: string;
  type: 'data-protection' | 'retention' | 'security' | 'consent' | 'transfer' | 'access-rights';
  jurisdictionId: string;
  requirements: PolicyRequirement[];
  applicableDataTypes: string[];
  enforcementLevel: 'mandatory' | 'recommended' | 'optional';
  penalties: string[];
  effectiveDate: string;
  expirationDate?: string;
  version: string;
  createdAt: string;
  updatedAt: string;
}

export interface Jurisdiction {
  id: string;
  name: string;
  code: string; // ISO country code or region code
  type: 'country' | 'state' | 'region' | 'economic-union';
  parentJurisdiction?: string; // for hierarchical jurisdictions
  regulations: string[]; // GDPR, CCPA, PIPEDA, etc.
  dataResidencyRequired: boolean;
  transferRestrictions: TransferRestriction[];
  contactAuthority?: string;
  language: string;
  currency?: string;
}

export interface ApprovalRecord {
  id: string;
  useCaseId: string;
  reviewType: 'initial' | 'periodic' | 'change-driven' | 'incident-response';
  stage1Review: ReviewStage;
  stage2Review: ReviewStage;
  finalApprovalStatus: 'pending' | 'approved' | 'rejected' | 'conditional';
  finalApprover?: string;
  finalApprovalDate?: string;
  conditions?: string[];
  validUntil?: string;
  legalNotes: string;
  attachments: string[];
  createdAt: string;
  updatedAt: string;
}

export interface AuditRecord {
  id: string;
  entityType: 'use-case' | 'application' | 'dataset' | 'policy' | 'approval';
  entityId: string;
  action: 'created' | 'updated' | 'deleted' | 'approved' | 'rejected' | 'accessed' | 'exported';
  actor: string;
  timestamp: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  hash: string; // for immutability verification
  previousHash?: string; // blockchain-style linking
}

// Supporting interfaces

export interface AccessControl {
  role: string;
  permissions: ('read' | 'write' | 'delete' | 'admin')[];
  conditions?: string[];
}

export interface BackupPolicy {
  frequency: 'daily' | 'weekly' | 'monthly';
  retentionPeriod: number; // in days
  location: 'same-region' | 'different-region' | 'different-country';
  encryptionRequired: boolean;
}

export interface PolicyRequirement {
  id: string;
  description: string;
  category: 'consent' | 'retention' | 'security' | 'access' | 'transfer' | 'notification';
  mandatory: boolean;
  verificationMethod: string;
  evidence?: string[];
}

export interface TransferRestriction {
  destinationJurisdiction: string;
  mechanism: 'adequacy-decision' | 'standard-clauses' | 'binding-rules' | 'derogation' | 'prohibited';
  conditions?: string[];
  approvalRequired: boolean;
}

export interface ReviewStage {
  reviewer: string;
  reviewDate?: string;
  status: 'pending' | 'in-progress' | 'completed' | 'requires-clarification';
  findings: string[];
  recommendations: string[];
  riskAssessment?: RiskAssessment;
  notes: string;
  attachments?: string[];
}

export interface RiskAssessment {
  overallRisk: 'low' | 'medium' | 'high' | 'very-high';
  riskFactors: RiskFactor[];
  mitigationMeasures: string[];
  residualRisk: 'low' | 'medium' | 'high' | 'very-high';
  reviewDate: string;
  nextReviewDate: string;
}

export interface RiskFactor {
  category: 'data-sensitivity' | 'volume' | 'international-transfer' | 'third-party' | 'retention' | 'consent';
  description: string;
  impact: 'low' | 'medium' | 'high' | 'very-high';
  likelihood: 'low' | 'medium' | 'high' | 'very-high';
  riskScore: number; // calculated from impact × likelihood
}
