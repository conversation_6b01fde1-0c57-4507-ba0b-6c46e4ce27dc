import {
  DataSource,
  DataSourceLocation,
  Policy,
  Jurisdiction,
  RiskAssessment
} from '../types';

// Define types locally to avoid circular dependency
interface SoftwareApplication {
  id: string;
  name: string;
  description: string;
  businessPurpose: string;
  owner: string;
  technicalContact: string;
  dataIngressPoints: DataIngressPoint[];
  dataProcessors: DataProcessor[];
  dataEgressPoints: DataEgressPoint[];
  datasets: string[];
  useCaseId: string;
  jurisdictions: string[];
  complianceStatus: 'compliant' | 'non-compliant' | 'under-review' | 'requires-attention';
  lastAuditDate?: string;
  nextAuditDate?: string;
  createdAt: string;
  updatedAt: string;
}

interface UseCase {
  id: string;
  name: string;
  description: string;
  businessJustification: string;
  legalBasis: string;
  dataSubjects: string[];
  applications: string[];
  approvalRecord?: ApprovalRecord;
  auditRecords: AuditRecord[];
  riskAssessment: RiskAssessment;
  owner: string;
  stakeholders: string[];
  createdAt: string;
  updatedAt: string;
}

interface DataIngressPoint {
  id: string;
  name: string;
  type: 'api' | 'file-upload' | 'database-sync' | 'stream' | 'manual-entry' | 'third-party';
  description: string;
  sourceSystem?: string;
  dataFormat: 'json' | 'xml' | 'csv' | 'binary' | 'structured' | 'unstructured';
  frequency: 'real-time' | 'batch' | 'scheduled' | 'on-demand';
  volumeEstimate?: string;
  securityMeasures: string[];
  monitoringEnabled: boolean;
}

interface DataProcessor {
  id: string;
  name: string;
  type: 'transformation' | 'enrichment' | 'validation' | 'anonymization' | 'aggregation' | 'analysis';
  description: string;
  processingLogic: string;
  inputDataTypes: string[];
  outputDataTypes: string[];
  retentionImpact: 'none' | 'extends' | 'reduces' | 'transforms';
  privacyImpact: 'none' | 'anonymizes' | 'pseudonymizes' | 'enriches' | 'exposes';
  automatedProcessing: boolean;
  humanReviewRequired: boolean;
}

interface DataEgressPoint {
  id: string;
  name: string;
  type: 'api' | 'file-export' | 'database-sync' | 'report' | 'third-party-sharing' | 'analytics';
  description: string;
  destinationSystem?: string;
  recipients: string[];
  dataFormat: 'json' | 'xml' | 'csv' | 'pdf' | 'dashboard' | 'api-response';
  frequency: 'real-time' | 'batch' | 'scheduled' | 'on-demand';
  securityMeasures: string[];
  internationalTransfer: boolean;
  transferMechanisms?: string[];
}

interface ApprovalRecord {
  id: string;
  useCaseId: string;
  reviewType: 'initial' | 'periodic' | 'change-driven' | 'incident-response';
  stage1Review: {
    reviewer: string;
    reviewDate?: string;
    status: 'pending' | 'in-progress' | 'completed' | 'requires-clarification';
    findings: string[];
    recommendations: string[];
    notes: string;
    attachments?: string[];
  };
  stage2Review: {
    reviewer: string;
    reviewDate?: string;
    status: 'pending' | 'in-progress' | 'completed' | 'requires-clarification';
    findings: string[];
    recommendations: string[];
    notes: string;
    attachments?: string[];
  };
  finalApprovalStatus: 'pending' | 'approved' | 'rejected' | 'conditional';
  finalApprover?: string;
  finalApprovalDate?: string;
  conditions?: string[];
  validUntil?: string;
  legalNotes: string;
  attachments: string[];
  createdAt: string;
  updatedAt: string;
}

interface AuditRecord {
  id: string;
  entityType: 'use-case' | 'application' | 'dataset' | 'policy' | 'approval';
  entityId: string;
  action: 'created' | 'updated' | 'deleted' | 'approved' | 'rejected' | 'accessed' | 'exported';
  actor: string;
  timestamp: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  hash: string;
  previousHash?: string;
}

// Jurisdictions
export const mockJurisdictions: Jurisdiction[] = [
  {
    id: 'eu',
    name: 'European Union',
    code: 'EU',
    type: 'economic-union',
    regulations: ['GDPR', 'ePrivacy Directive', 'Data Governance Act'],
    dataResidencyRequired: true,
    transferRestrictions: [
      {
        destinationJurisdiction: 'us',
        mechanism: 'standard-clauses',
        conditions: ['Adequate safeguards required'],
        approvalRequired: true
      }
    ],
    contactAuthority: 'European Data Protection Board',
    language: 'en',
    currency: 'EUR'
  },
  {
    id: 'us',
    name: 'United States',
    code: 'US',
    type: 'country',
    regulations: ['CCPA', 'COPPA', 'HIPAA', 'SOX'],
    dataResidencyRequired: false,
    transferRestrictions: [],
    contactAuthority: 'Federal Trade Commission',
    language: 'en',
    currency: 'USD'
  },
  {
    id: 'ca',
    name: 'Canada',
    code: 'CA',
    type: 'country',
    regulations: ['PIPEDA', 'PIPA'],
    dataResidencyRequired: false,
    transferRestrictions: [
      {
        destinationJurisdiction: 'cn',
        mechanism: 'prohibited',
        conditions: ['Government data prohibited'],
        approvalRequired: false
      }
    ],
    contactAuthority: 'Office of the Privacy Commissioner',
    language: 'en',
    currency: 'CAD'
  }
];

// Data Source Locations
export const mockDataSourceLocations: DataSourceLocation[] = [
  {
    id: 'dublin-dc1',
    name: 'Dublin Data Center 1',
    country: 'Ireland',
    region: 'EU-West-1',
    dataCenter: 'AWS Dublin',
    coordinates: { latitude: 53.3498, longitude: -6.2603 },
    jurisdictionId: 'eu',
    certifications: ['ISO 27001', 'SOC 2 Type II', 'PCI DSS'],
    residencyRequirements: ['EU data residency compliant']
  },
  {
    id: 'virginia-dc1',
    name: 'Virginia Data Center 1',
    country: 'United States',
    region: 'US-East-1',
    dataCenter: 'AWS Virginia',
    coordinates: { latitude: 39.0458, longitude: -77.5091 },
    jurisdictionId: 'us',
    certifications: ['SOC 2 Type II', 'FedRAMP', 'HIPAA'],
    residencyRequirements: []
  },
  {
    id: 'toronto-dc1',
    name: 'Toronto Data Center 1',
    country: 'Canada',
    region: 'CA-Central-1',
    dataCenter: 'AWS Canada Central',
    coordinates: { latitude: 43.6532, longitude: -79.3832 },
    jurisdictionId: 'ca',
    certifications: ['ISO 27001', 'SOC 2 Type II'],
    residencyRequirements: ['Canadian data residency for government data']
  }
];

// Data Sources
export const mockDataSources: DataSource[] = [
  {
    id: 'customer-db-prod',
    name: 'Customer Database (Production)',
    description: 'Primary customer data repository containing user profiles, preferences, and transaction history',
    type: 'database',
    connectionString: 'postgresql://prod-db.internal:5432/customers',
    location: mockDataSourceLocations[0], // Dublin
    datasets: ['1', '2'], // Customer Purchase History, Website Analytics
    securityClassification: 'confidential',
    encryptionStatus: 'encrypted',
    accessControls: [
      { role: 'data-engineer', permissions: ['read'], conditions: ['VPN required'] },
      { role: 'privacy-officer', permissions: ['read', 'admin'], conditions: ['MFA required'] },
      { role: 'dba', permissions: ['read', 'write', 'admin'], conditions: ['Approval required'] }
    ],
    backupPolicy: {
      frequency: 'daily',
      retentionPeriod: 90,
      location: 'different-region',
      encryptionRequired: true
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-07-15T10:30:00Z'
  },
  {
    id: 'hr-system-prod',
    name: 'HR Information System',
    description: 'Employee data management system containing personal information, payroll, and performance data',
    type: 'database',
    connectionString: 'postgresql://hr-db.internal:5432/hrms',
    location: mockDataSourceLocations[1], // Virginia
    datasets: ['3'], // Employee HR Records
    securityClassification: 'highly-sensitive',
    encryptionStatus: 'encrypted',
    accessControls: [
      { role: 'hr-manager', permissions: ['read', 'write'], conditions: ['MFA required', 'Business hours only'] },
      { role: 'privacy-officer', permissions: ['read', 'admin'], conditions: ['MFA required'] },
      { role: 'payroll-admin', permissions: ['read'], conditions: ['Approval required', 'Audit logged'] }
    ],
    backupPolicy: {
      frequency: 'daily',
      retentionPeriod: 2555, // 7 years
      location: 'same-region',
      encryptionRequired: true
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-07-15T10:30:00Z'
  }
];

// Policies
export const mockPolicies: Policy[] = [
  {
    id: 'gdpr-retention',
    name: 'GDPR Data Retention Policy',
    description: 'Data retention requirements under the General Data Protection Regulation',
    type: 'retention',
    jurisdictionId: 'eu',
    requirements: [
      {
        id: 'req-1',
        description: 'Personal data must not be kept longer than necessary for the purposes for which it was collected',
        category: 'retention',
        mandatory: true,
        verificationMethod: 'Automated retention policy enforcement',
        evidence: ['Retention policy documentation', 'Automated deletion logs']
      },
      {
        id: 'req-2',
        description: 'Data subjects have the right to request deletion of their personal data',
        category: 'access',
        mandatory: true,
        verificationMethod: 'Data subject request handling process',
        evidence: ['Request handling logs', 'Deletion confirmation records']
      }
    ],
    applicableDataTypes: ['personal-data', 'sensitive-personal-data'],
    enforcementLevel: 'mandatory',
    penalties: ['Up to 4% of annual global turnover or €20 million, whichever is higher'],
    effectiveDate: '2018-05-25',
    version: '1.0',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'ccpa-consent',
    name: 'CCPA Consent Requirements',
    description: 'Consumer consent requirements under the California Consumer Privacy Act',
    type: 'consent',
    jurisdictionId: 'us',
    requirements: [
      {
        id: 'req-1',
        description: 'Consumers must be informed about data collection at or before the point of collection',
        category: 'consent',
        mandatory: true,
        verificationMethod: 'Privacy notice review and consent tracking',
        evidence: ['Privacy notices', 'Consent records', 'User interface screenshots']
      }
    ],
    applicableDataTypes: ['personal-information', 'sensitive-personal-information'],
    enforcementLevel: 'mandatory',
    penalties: ['Up to $2,500 per violation or $7,500 for intentional violations'],
    effectiveDate: '2020-01-01',
    version: '2.0',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-07-01T00:00:00Z'
  }
];

// Risk Assessment
const sampleRiskAssessment: RiskAssessment = {
  overallRisk: 'medium',
  riskFactors: [
    {
      category: 'data-sensitivity',
      description: 'Processing of personal and financial data',
      impact: 'high',
      likelihood: 'medium',
      riskScore: 6
    },
    {
      category: 'international-transfer',
      description: 'Data transfers between EU and US',
      impact: 'medium',
      likelihood: 'high',
      riskScore: 6
    }
  ],
  mitigationMeasures: [
    'Standard Contractual Clauses implemented',
    'Data encryption in transit and at rest',
    'Regular security audits and penetration testing',
    'Staff privacy training program'
  ],
  residualRisk: 'low',
  reviewDate: '2024-06-01',
  nextReviewDate: '2024-12-01'
};

// Use Cases
export const mockUseCases: UseCase[] = [
  {
    id: 'ecommerce-platform',
    name: 'E-commerce Platform Operations',
    description: 'Complete customer journey from registration to purchase completion including analytics and marketing',
    businessJustification: 'Enable online sales, customer service, and business intelligence for revenue generation and customer satisfaction',
    legalBasis: 'Contract performance and legitimate business interest',
    dataSubjects: ['Customers', 'Website visitors', 'Registered users'],
    applications: ['customer-web-app', 'analytics-platform'],
    riskAssessment: sampleRiskAssessment,
    owner: 'Sarah Johnson, VP of Digital Commerce',
    stakeholders: ['Marketing Team', 'Customer Service', 'IT Security', 'Legal Department'],
    auditRecords: [],
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-07-15T10:30:00Z'
  },
  {
    id: 'hr-management',
    name: 'Human Resources Management',
    description: 'Employee lifecycle management from recruitment to termination including payroll and performance management',
    businessJustification: 'Legal requirement for employment administration, payroll processing, and regulatory compliance',
    legalBasis: 'Legal obligation and legitimate business interest',
    dataSubjects: ['Employees', 'Job applicants', 'Former employees'],
    applications: ['hr-system'],
    riskAssessment: {
      ...sampleRiskAssessment,
      overallRisk: 'high',
      riskFactors: [
        {
          category: 'data-sensitivity',
          description: 'Processing of highly sensitive employee data including SSN and salary information',
          impact: 'very-high',
          likelihood: 'medium',
          riskScore: 8
        }
      ]
    },
    owner: 'Michael Chen, CHRO',
    stakeholders: ['HR Team', 'Payroll Department', 'Legal Department', 'IT Security'],
    auditRecords: [],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-07-15T10:30:00Z'
  }
];

// Data Flow Components
const customerDataIngress: DataIngressPoint[] = [
  {
    id: 'web-registration',
    name: 'Web Registration Form',
    type: 'manual-entry',
    description: 'Customer registration through website form',
    sourceSystem: 'Customer Web Portal',
    dataFormat: 'json',
    frequency: 'real-time',
    volumeEstimate: '~500 registrations/day',
    securityMeasures: ['HTTPS encryption', 'CAPTCHA protection', 'Rate limiting'],
    monitoringEnabled: true
  },
  {
    id: 'payment-api',
    name: 'Payment Processing API',
    type: 'api',
    description: 'Real-time payment transaction data from payment processor',
    sourceSystem: 'Stripe Payment Gateway',
    dataFormat: 'json',
    frequency: 'real-time',
    volumeEstimate: '~2000 transactions/day',
    securityMeasures: ['TLS 1.3', 'API key authentication', 'Webhook signature verification'],
    monitoringEnabled: true
  }
];

const customerDataProcessors: DataProcessor[] = [
  {
    id: 'data-validation',
    name: 'Customer Data Validation',
    type: 'validation',
    description: 'Validates customer input data for completeness and format compliance',
    processingLogic: 'Email format validation, phone number normalization, address standardization',
    inputDataTypes: ['customer-profile', 'contact-information'],
    outputDataTypes: ['validated-customer-profile'],
    retentionImpact: 'none',
    privacyImpact: 'none',
    automatedProcessing: true,
    humanReviewRequired: false
  },
  {
    id: 'purchase-analytics',
    name: 'Purchase Behavior Analytics',
    type: 'analysis',
    description: 'Analyzes customer purchase patterns for business intelligence',
    processingLogic: 'Aggregates transaction data, calculates customer lifetime value, identifies trends',
    inputDataTypes: ['transaction-data', 'customer-profile'],
    outputDataTypes: ['analytics-insights', 'customer-segments'],
    retentionImpact: 'extends',
    privacyImpact: 'anonymizes',
    automatedProcessing: true,
    humanReviewRequired: false
  }
];

const customerDataEgress: DataEgressPoint[] = [
  {
    id: 'marketing-export',
    name: 'Marketing Campaign Export',
    type: 'file-export',
    description: 'Export customer segments for targeted marketing campaigns',
    destinationSystem: 'Marketing Automation Platform',
    recipients: ['Marketing Team', 'External Marketing Agency'],
    dataFormat: 'csv',
    frequency: 'weekly',
    securityMeasures: ['Encrypted file transfer', 'Access logging', 'Data minimization'],
    internationalTransfer: false,
    transferMechanisms: []
  },
  {
    id: 'analytics-dashboard',
    name: 'Executive Analytics Dashboard',
    type: 'dashboard',
    description: 'Real-time business metrics and customer insights for leadership',
    destinationSystem: 'Business Intelligence Platform',
    recipients: ['Executive Team', 'Business Analysts'],
    dataFormat: 'dashboard',
    frequency: 'real-time',
    securityMeasures: ['Role-based access control', 'Session timeout', 'Audit logging'],
    internationalTransfer: false,
    transferMechanisms: []
  }
];

// Software Applications
export const mockSoftwareApplications: SoftwareApplication[] = [
  {
    id: 'customer-web-app',
    name: 'Customer Web Application',
    description: 'Primary e-commerce web application handling customer registration, authentication, and purchase transactions',
    businessPurpose: 'Enable online customer interactions, sales transactions, and customer service',
    owner: 'Sarah Johnson, VP of Digital Commerce',
    technicalContact: 'Alex Rodriguez, Lead Developer',
    dataIngressPoints: customerDataIngress,
    dataProcessors: customerDataProcessors,
    dataEgressPoints: customerDataEgress,
    datasets: ['1'], // Customer Purchase History
    useCaseId: 'ecommerce-platform',
    jurisdictions: ['eu', 'us'],
    complianceStatus: 'compliant',
    lastAuditDate: '2024-06-15',
    nextAuditDate: '2024-12-15',
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-07-15T10:30:00Z'
  },
  {
    id: 'analytics-platform',
    name: 'Customer Analytics Platform',
    description: 'Data analytics and business intelligence platform for customer behavior analysis',
    businessPurpose: 'Generate business insights from customer data to improve products and services',
    owner: 'David Kim, VP of Analytics',
    technicalContact: 'Maria Garcia, Data Engineering Lead',
    dataIngressPoints: [
      {
        id: 'web-analytics-stream',
        name: 'Web Analytics Data Stream',
        type: 'stream',
        description: 'Real-time website interaction data',
        sourceSystem: 'Google Analytics',
        dataFormat: 'json',
        frequency: 'real-time',
        volumeEstimate: '~50,000 events/day',
        securityMeasures: ['API authentication', 'Data encryption', 'Rate limiting'],
        monitoringEnabled: true
      }
    ],
    dataProcessors: [
      {
        id: 'behavioral-analysis',
        name: 'Customer Behavior Analysis',
        type: 'analysis',
        description: 'Analyzes customer website behavior and interaction patterns',
        processingLogic: 'Session analysis, funnel tracking, cohort analysis',
        inputDataTypes: ['web-analytics', 'session-data'],
        outputDataTypes: ['behavior-insights', 'user-segments'],
        retentionImpact: 'transforms',
        privacyImpact: 'anonymizes',
        automatedProcessing: true,
        humanReviewRequired: false
      }
    ],
    dataEgressPoints: [
      {
        id: 'insights-api',
        name: 'Analytics Insights API',
        type: 'api',
        description: 'Provides analytics insights to other internal systems',
        destinationSystem: 'Various Internal Applications',
        recipients: ['Product Team', 'Marketing Team', 'Executive Team'],
        dataFormat: 'json',
        frequency: 'on-demand',
        securityMeasures: ['API authentication', 'Rate limiting', 'Data anonymization'],
        internationalTransfer: false,
        transferMechanisms: []
      }
    ],
    datasets: ['2'], // Website Analytics
    useCaseId: 'ecommerce-platform',
    jurisdictions: ['eu', 'us'],
    complianceStatus: 'under-review',
    lastAuditDate: '2024-05-01',
    nextAuditDate: '2024-11-01',
    createdAt: '2024-02-01T00:00:00Z',
    updatedAt: '2024-07-15T10:30:00Z'
  },
  {
    id: 'hr-system',
    name: 'Human Resources Information System',
    description: 'Comprehensive HR management system for employee data, payroll, and performance management',
    businessPurpose: 'Manage employee lifecycle, payroll processing, and regulatory compliance',
    owner: 'Michael Chen, CHRO',
    technicalContact: 'Jennifer Liu, HR Systems Administrator',
    dataIngressPoints: [
      {
        id: 'employee-onboarding',
        name: 'Employee Onboarding Portal',
        type: 'manual-entry',
        description: 'New employee data entry during onboarding process',
        sourceSystem: 'HR Onboarding Portal',
        dataFormat: 'structured',
        frequency: 'on-demand',
        volumeEstimate: '~50 new employees/month',
        securityMeasures: ['Multi-factor authentication', 'Encrypted forms', 'Access logging'],
        monitoringEnabled: true
      }
    ],
    dataProcessors: [
      {
        id: 'payroll-calculation',
        name: 'Payroll Processing',
        type: 'transformation',
        description: 'Calculates employee compensation based on hours, salary, and deductions',
        processingLogic: 'Salary calculation, tax deductions, benefits processing',
        inputDataTypes: ['employee-data', 'timesheet-data'],
        outputDataTypes: ['payroll-data', 'tax-documents'],
        retentionImpact: 'extends',
        privacyImpact: 'none',
        automatedProcessing: true,
        humanReviewRequired: true
      }
    ],
    dataEgressPoints: [
      {
        id: 'payroll-export',
        name: 'Payroll Data Export',
        type: 'file-export',
        description: 'Export payroll data to external payroll processing service',
        destinationSystem: 'ADP Payroll Service',
        recipients: ['Payroll Department', 'ADP Service'],
        dataFormat: 'csv',
        frequency: 'monthly',
        securityMeasures: ['File encryption', 'Secure FTP', 'Access controls'],
        internationalTransfer: false,
        transferMechanisms: []
      }
    ],
    datasets: ['3'], // Employee HR Records
    useCaseId: 'hr-management',
    jurisdictions: ['us'],
    complianceStatus: 'compliant',
    lastAuditDate: '2024-07-01',
    nextAuditDate: '2025-01-01',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-07-15T10:30:00Z'
  }
];

// Approval Records
export const mockApprovalRecords: ApprovalRecord[] = [
  {
    id: 'approval-ecommerce-2024',
    useCaseId: 'ecommerce-platform',
    reviewType: 'periodic',
    stage1Review: {
      reviewer: 'Jane Smith, Privacy Officer',
      reviewDate: '2024-06-01',
      status: 'completed',
      findings: [
        'Data minimization principles properly applied',
        'Consent mechanisms are granular and user-friendly',
        'International transfer safeguards are adequate'
      ],
      recommendations: [
        'Consider implementing automated data retention cleanup',
        'Enhance monitoring of third-party data processors',
        'Update privacy notices to reflect new processing activities'
      ],
      notes: 'Overall compliance posture is strong. Minor improvements recommended for operational efficiency.',
      attachments: ['privacy-impact-assessment.pdf', 'data-flow-diagram.png']
    },
    stage2Review: {
      reviewer: 'Robert Wilson, Legal Counsel',
      reviewDate: '2024-06-15',
      status: 'completed',
      findings: [
        'Legal basis for processing is clearly established',
        'Data subject rights procedures are compliant',
        'Cross-border transfer mechanisms are legally sound'
      ],
      recommendations: [
        'Update data processing agreements with vendors',
        'Conduct annual legal compliance review',
        'Implement breach notification procedures'
      ],
      notes: 'Legal review confirms compliance with applicable regulations. Recommended improvements will strengthen legal posture.',
      attachments: ['legal-opinion.pdf', 'vendor-agreements.zip']
    },
    finalApprovalStatus: 'approved',
    finalApprover: 'Emily Davis, Chief Privacy Officer',
    finalApprovalDate: '2024-06-20',
    conditions: [
      'Implement automated retention cleanup within 90 days',
      'Update vendor agreements within 60 days',
      'Conduct quarterly compliance monitoring'
    ],
    validUntil: '2025-06-20',
    legalNotes: 'Approval granted subject to implementation of recommended improvements. Next review scheduled for Q2 2025.',
    attachments: ['final-approval-memo.pdf', 'compliance-checklist.xlsx'],
    createdAt: '2024-06-01T00:00:00Z',
    updatedAt: '2024-06-20T15:30:00Z'
  }
];

// Audit Records
export const mockAuditRecords: AuditRecord[] = [
  {
    id: 'audit-001',
    entityType: 'use-case',
    entityId: 'ecommerce-platform',
    action: 'approved',
    actor: 'Emily Davis, Chief Privacy Officer',
    timestamp: '2024-06-20T15:30:00Z',
    details: {
      approvalId: 'approval-ecommerce-2024',
      conditions: 3,
      validUntil: '2025-06-20'
    },
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    hash: 'a1b2c3d4e5f6789012345678901234567890abcd',
    previousHash: '9876543210fedcba0987654321098765432109876'
  },
  {
    id: 'audit-002',
    entityType: 'application',
    entityId: 'customer-web-app',
    action: 'updated',
    actor: 'Alex Rodriguez, Lead Developer',
    timestamp: '2024-07-15T10:30:00Z',
    details: {
      changes: ['Updated data retention policy', 'Added new data processor'],
      version: '2.1.0'
    },
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    hash: 'b2c3d4e5f6789012345678901234567890abcde1',
    previousHash: 'a1b2c3d4e5f6789012345678901234567890abcd'
  }
];