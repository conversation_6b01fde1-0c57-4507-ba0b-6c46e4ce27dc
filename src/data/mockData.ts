// Define types locally to avoid circular dependency issues
type Dataset = any;
type PrivacyReview = any;
type ComplianceReport = any;
type DashboardMetrics = any;

export const mockDatasets: Dataset[] = [
  {
    id: '1',
    name: 'Customer Purchase History',
    description: 'Historical transaction data from e-commerce platform including purchase patterns and preferences',
    source: 'E-commerce Platform API',
    collectionDate: '2024-01-15',
    collectionFrequency: 'daily',
    dataTypes: [
      { name: 'customer_id', type: 'string', isPII: true, isSensitive: false, description: 'Unique customer identifier' },
      { name: 'email', type: 'string', isPII: true, isSensitive: false, description: 'Customer email address' },
      { name: 'purchase_amount', type: 'number', isPII: false, isSensitive: false, description: 'Transaction amount' },
      { name: 'product_category', type: 'string', isPII: false, isSensitive: false, description: 'Product category' }
    ],
    schema: [
      { fieldName: 'customer_id', dataType: 'VARCHAR(50)', isRequired: true, isPII: true, description: 'Primary key' },
      { fieldName: 'email', dataType: 'VARCHAR(255)', isRequired: true, isPII: true, description: 'Contact email' },
      { fieldName: 'purchase_date', dataType: 'TIMESTAMP', isRequired: true, isPII: false, description: 'Transaction timestamp' },
      { fieldName: 'amount', dataType: 'DECIMAL(10,2)', isRequired: true, isPII: false, description: 'Purchase amount in USD' }
    ],
    consentStatus: {
      hasConsent: true,
      consentType: 'explicit',
      consentDate: '2024-01-10',
      consentExpiry: '2026-01-10',
      withdrawalMechanism: 'Customer portal or email request',
      granularConsent: [
        { purpose: 'Transaction processing', hasConsent: true, consentDate: '2024-01-10' },
        { purpose: 'Marketing communications', hasConsent: true, consentDate: '2024-01-10' },
        { purpose: 'Analytics and insights', hasConsent: false }
      ]
    },
    privacyClassification: 'confidential',
    retentionPolicy: {
      retentionPeriod: 2555, // 7 years
      retentionReason: 'Legal compliance and fraud prevention',
      disposalMethod: 'anonymize',
      expirationDate: '2031-01-15',
      autoDelete: true
    },
    complianceStatus: 'compliant',
    lastReviewDate: '2024-06-15',
    nextReviewDate: '2024-12-15',
    tags: ['e-commerce', 'financial', 'customer-data'],
    owner: 'Data Engineering Team',
    createdAt: '2024-01-15',
    updatedAt: '2024-06-15'
  },
  {
    id: '2',
    name: 'User Behavior Analytics',
    description: 'Website and mobile app user interaction data for product optimization',
    source: 'Google Analytics & Mobile SDK',
    collectionDate: '2024-02-01',
    collectionFrequency: 'daily',
    dataTypes: [
      { name: 'session_id', type: 'string', isPII: false, isSensitive: false, description: 'Anonymous session identifier' },
      { name: 'page_views', type: 'array', isPII: false, isSensitive: false, description: 'Pages visited during session' },
      { name: 'device_info', type: 'object', isPII: false, isSensitive: false, description: 'Device and browser information' },
      { name: 'ip_address', type: 'string', isPII: true, isSensitive: false, description: 'User IP address (hashed)' }
    ],
    schema: [
      { fieldName: 'session_id', dataType: 'VARCHAR(100)', isRequired: true, isPII: false, description: 'Unique session ID' },
      { fieldName: 'user_agent', dataType: 'TEXT', isRequired: false, isPII: false, description: 'Browser user agent' },
      { fieldName: 'timestamp', dataType: 'TIMESTAMP', isRequired: true, isPII: false, description: 'Event timestamp' },
      { fieldName: 'event_data', dataType: 'JSON', isRequired: false, isPII: false, description: 'Event metadata' }
    ],
    consentStatus: {
      hasConsent: true,
      consentType: 'implicit',
      consentDate: '2024-02-01',
      withdrawalMechanism: 'Cookie preferences or opt-out link',
      granularConsent: [
        { purpose: 'Website functionality', hasConsent: true, consentDate: '2024-02-01' },
        { purpose: 'Analytics', hasConsent: true, consentDate: '2024-02-01' },
        { purpose: 'Personalization', hasConsent: false }
      ]
    },
    privacyClassification: 'internal',
    retentionPolicy: {
      retentionPeriod: 730, // 2 years
      retentionReason: 'Product improvement and user experience optimization',
      disposalMethod: 'delete',
      expirationDate: '2026-02-01',
      autoDelete: true
    },
    complianceStatus: 'under-review',
    lastReviewDate: '2024-05-01',
    nextReviewDate: '2024-11-01',
    tags: ['analytics', 'behavioral', 'product'],
    owner: 'Product Analytics Team',
    createdAt: '2024-02-01',
    updatedAt: '2024-05-01'
  },
  {
    id: '3',
    name: 'Employee HR Records',
    description: 'Comprehensive employee data including personal information, performance, and compensation',
    source: 'HRIS System',
    collectionDate: '2024-01-01',
    collectionFrequency: 'monthly',
    dataTypes: [
      { name: 'employee_id', type: 'string', isPII: true, isSensitive: false, description: 'Employee identifier' },
      { name: 'ssn', type: 'string', isPII: true, isSensitive: true, description: 'Social Security Number' },
      { name: 'salary', type: 'number', isPII: false, isSensitive: true, description: 'Employee compensation' },
      { name: 'performance_rating', type: 'string', isPII: false, isSensitive: true, description: 'Annual performance score' }
    ],
    schema: [
      { fieldName: 'employee_id', dataType: 'VARCHAR(20)', isRequired: true, isPII: true, description: 'Primary key' },
      { fieldName: 'first_name', dataType: 'VARCHAR(50)', isRequired: true, isPII: true, description: 'Employee first name' },
      { fieldName: 'last_name', dataType: 'VARCHAR(50)', isRequired: true, isPII: true, description: 'Employee last name' },
      { fieldName: 'ssn_encrypted', dataType: 'VARCHAR(255)', isRequired: true, isPII: true, description: 'Encrypted SSN' }
    ],
    consentStatus: {
      hasConsent: true,
      consentType: 'legitimate-interest',
      consentDate: '2024-01-01',
      withdrawalMechanism: 'HR department request (limited by employment law)',
      granularConsent: [
        { purpose: 'Employment administration', hasConsent: true, consentDate: '2024-01-01' },
        { purpose: 'Payroll processing', hasConsent: true, consentDate: '2024-01-01' },
        { purpose: 'Benefits administration', hasConsent: true, consentDate: '2024-01-01' }
      ]
    },
    privacyClassification: 'highly-sensitive',
    retentionPolicy: {
      retentionPeriod: 2555, // 7 years after employment ends
      retentionReason: 'Legal compliance and employment law requirements',
      disposalMethod: 'delete',
      expirationDate: '2031-01-01',
      autoDelete: false
    },
    complianceStatus: 'compliant',
    lastReviewDate: '2024-07-01',
    nextReviewDate: '2025-01-01',
    tags: ['hr', 'employee', 'sensitive', 'pii'],
    owner: 'Human Resources',
    createdAt: '2024-01-01',
    updatedAt: '2024-07-01'
  }
];

export const mockPrivacyReviews: PrivacyReview[] = [
  {
    id: '1',
    datasetId: '1',
    reviewType: 'periodic',
    reviewDate: '2024-06-15',
    reviewer: 'Jane Smith, Privacy Officer',
    status: 'completed',
    privacyImpactAssessment: {
      dataProcessingPurpose: 'Customer transaction processing and business analytics',
      legalBasis: 'Contract performance and legitimate business interest',
      dataSubjects: ['Customers', 'Website visitors'],
      dataCategories: ['Contact information', 'Transaction data', 'Behavioral data'],
      recipients: ['Internal teams', 'Payment processors', 'Analytics vendors'],
      internationalTransfers: true,
      transferMechanisms: ['Standard Contractual Clauses', 'Adequacy decisions'],
      retentionJustification: 'Required for fraud prevention and regulatory compliance',
      riskLevel: 'medium',
      riskMitigations: ['Data encryption', 'Access controls', 'Regular audits'],
      dpoConsultation: true,
      publicConsultation: false
    },
    findings: [
      {
        id: '1',
        category: 'consent',
        severity: 'medium',
        description: 'Marketing consent mechanism could be more granular',
        recommendation: 'Implement separate consent checkboxes for different marketing channels',
        status: 'in-progress',
        dueDate: '2024-09-15',
        assignee: 'Marketing Team'
      }
    ],
    recommendations: [
      'Enhance consent granularity for marketing purposes',
      'Implement automated data retention cleanup',
      'Review third-party data sharing agreements'
    ],
    nextReviewDate: '2024-12-15',
    approvalStatus: 'approved',
    approver: 'Chief Privacy Officer',
    approvalDate: '2024-06-20'
  }
];

export const mockComplianceReports: ComplianceReport[] = [
  {
    id: '1',
    title: 'Q2 2024 GDPR Compliance Report',
    type: 'gdpr',
    generatedDate: '2024-07-01',
    reportPeriod: {
      startDate: '2024-04-01',
      endDate: '2024-06-30'
    },
    datasets: ['1', '2', '3'],
    metrics: [
      { name: 'Data Subject Requests', value: 45, unit: 'requests', target: 50, status: 'good', trend: 'stable' },
      { name: 'Response Time', value: 18, unit: 'days', target: 30, status: 'good', trend: 'improving' },
      { name: 'Consent Rate', value: 78, unit: 'percentage', target: 75, status: 'good', trend: 'improving' },
      { name: 'Data Breaches', value: 0, unit: 'incidents', target: 0, status: 'good', trend: 'stable' }
    ],
    summary: 'Overall compliance posture remains strong with all key metrics meeting targets.',
    recommendations: [
      'Continue monitoring consent rates across all touchpoints',
      'Enhance data subject request automation',
      'Review retention policies for legacy datasets'
    ],
    status: 'final'
  }
];

export const mockDashboardMetrics: DashboardMetrics = {
  totalDatasets: 3,
  compliantDatasets: 2,
  pendingReviews: 1,
  expiringSoon: 0,
  highRiskDatasets: 1,
  consentWithdrawals: 2,
  dataBreaches: 0,
  complianceScore: 87
};
