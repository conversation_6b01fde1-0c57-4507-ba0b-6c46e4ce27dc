:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Force light theme only - Apple style */
  color-scheme: light;
  color: #1D1D1F;
  background-color: #FFFFFF;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #007AFF;
  text-decoration: inherit;
}
a:hover {
  color: #0051D5;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid #D2D2D7;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #F8F9FA;
  color: #1D1D1F;
  cursor: pointer;
  transition: all 0.25s ease;
}
button:hover {
  border-color: #007AFF;
  background-color: #F2F2F7;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
button:focus,
button:focus-visible {
  outline: 2px solid #007AFF;
  outline-offset: 2px;
}

/* Remove media query for consistent light theme */
